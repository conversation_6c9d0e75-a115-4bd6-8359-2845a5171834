﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.Core.Web.Enums;
using PharmaLex.VigiLit.Aggregators.PubMed.Scheduled;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.Scraping.Client;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Authorize(Policy = Policies.SuperAdmin)]
[Route("[controller]")]
public class ToolsController : BaseController
{
    private readonly IImportQueueService _importQueueService;
    private readonly IEmailService _emailService;
    private readonly ICaseFilesEmailService _caseFilesEmailService;
    private readonly IVigiLitScrapingClient _scrapingClient;

    public ToolsController(
        IImportQueueService importQueueService,
        IEmailService emailService,
        ICaseFilesEmailService caseFilesEmailService,
        IVigiLitScrapingClient scrapingClient,
        IUserSessionService userSessionService,
        IConfiguration configuration) : base(userSessionService, configuration)
    {
        _importQueueService = importQueueService;
        _emailService = emailService;
        _caseFilesEmailService = caseFilesEmailService;
        _scrapingClient = scrapingClient;
    }

    [HttpGet]
    public IActionResult Index()
    {
        return View();
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> EnqueueScheduledImport()
    {
        await _importQueueService.EnqueueScheduledImport(ImportTriggerType.Manual);

        AddNotification("Executed: Enqueue Scheduled Import.", UserNotificationType.Confirm);

        return RedirectToAction("ImportLog", "Import");
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> RestoreJournalSchedules()
    {
        await _scrapingClient.Send();

        AddNotification($"Journal schedule restoration completed successfully.", UserNotificationType.Confirm);

        return RedirectToAction("Index");
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> SendDailyReferenceClassificationEmails()
    {
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Manual);

        AddNotification("Executed: Send Daily Reference Classification Emails", UserNotificationType.Confirm);

        return RedirectToAction("Index", "Emails");
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> SendDailyCaseFilesEmails()
    {
        await _caseFilesEmailService.Send(EmailTriggerType.Manual);

        AddNotification("Executed: Send Daily Case Files Emails", UserNotificationType.Confirm);

        return RedirectToAction("Index", "Emails");
    }
}
namespace PharmaLex.VigiLit.Scraping.Client;

/// <summary>
/// Result of the restore schedules operation.
/// Contains statistics and details about the restoration process.
/// </summary>
public class RestoreSchedulesResult
{
    /// <summary>
    /// Gets or sets the number of schedules created during the operation.
    /// </summary>
    public int SchedulesCreated { get; set; }

    /// <summary>
    /// Gets or sets the number of tasks created during the operation.
    /// </summary>
    public int TasksCreated { get; set; }

    /// <summary>
    /// Gets or sets the number of webhooks created during the operation.
    /// </summary>
    public int WebhooksCreated { get; set; }

    /// <summary>
    /// Gets or sets the number of journals processed during the operation.
    /// </summary>
    public int JournalsProcessed { get; set; }

    /// <summary>
    /// Gets or sets the list of errors that occurred during the operation.
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of informational messages from the operation.
    /// </summary>
    public List<string> Messages { get; set; } = new();

    /// <summary>
    /// Gets a value indicating whether the operation completed successfully.
    /// Returns true if there are no errors.
    /// </summary>
    public bool IsSuccess => Errors.Count == 0;

    /// <summary>
    /// Gets a summary of the operation results.
    /// </summary>
    public string Summary => $"Processed {JournalsProcessed} journals, created {TasksCreated} tasks, {SchedulesCreated} schedules, and {WebhooksCreated} webhooks. {Errors.Count} errors occurred.";
}

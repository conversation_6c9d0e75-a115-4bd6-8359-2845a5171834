namespace PharmaLex.VigiLit.Scraping.Client;

public interface IVigiLitScrapingClient
{
    /// <summary>
    /// Sends a restore schedules command to restore Apify schedules for all enabled journals.
    /// This is a legacy method that uses default command parameters.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task Send();

    /// <summary>
    /// Sends a restore schedules command with the specified parameters.
    /// </summary>
    /// <param name="command">The restore schedules command with configuration options.</param>
    /// <returns>A task representing the asynchronous operation that returns the operation result.</returns>
    Task<RestoreSchedulesResult> Send(RestoreSchedulesCommand command);
}

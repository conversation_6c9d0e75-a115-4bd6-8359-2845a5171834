using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Service.Services;

namespace PharmaLex.VigiLit.Scraping.Service.Clients;

public class VigiLitScrapingClient : IVigiLitScrapingClient
{
    private readonly IRestoreSchedulesCommandHandler _restoreSchedulesHandler;

    public VigiLitScrapingClient(IRestoreSchedulesCommandHandler restoreSchedulesHandler)
    {
        _restoreSchedulesHandler = restoreSchedulesHandler;
    }

    public async Task Send()
    {
        await _restoreSchedulesHandler.Consume();
    }

    public async Task<RestoreSchedulesResult> Send(RestoreSchedulesCommand command)
    {
        // Convert service result to client result
        var serviceResult = await _restoreSchedulesHandler.Consume(command.CancellationToken);

        return new RestoreSchedulesResult
        {
            SchedulesCreated = serviceResult.SchedulesCreated,
            TasksCreated = serviceResult.TasksCreated,
            WebhooksCreated = serviceResult.WebhooksCreated,
            JournalsProcessed = serviceResult.JournalsProcessed,
            Errors = serviceResult.Errors,
            Messages = serviceResult.Messages
        };
    }
}

namespace PharmaLex.VigiLit.Scraping.Client;

/// <summary>
/// Command to restore Apify schedules for all enabled journals.
/// This command triggers the creation/restoration of Apify tasks, schedules, and webhooks
/// for journals that have scheduling enabled.
/// </summary>
public class RestoreSchedulesCommand
{
    /// <summary>
    /// Gets or sets the cancellation token for the operation.
    /// </summary>
    public CancellationToken CancellationToken { get; set; } = default;

    /// <summary>
    /// Gets or sets a value indicating whether to force recreation of existing schedules.
    /// When true, existing schedules will be deleted and recreated.
    /// When false, only missing schedules will be created.
    /// </summary>
    public bool ForceRecreate { get; set; } = false;

    /// <summary>
    /// Gets or sets the optional filter for journal IDs to process.
    /// If null or empty, all enabled journals with schedules will be processed.
    /// </summary>
    public List<int>? JournalIds { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to run in dry-run mode.
    /// When true, the operation will validate and report what would be done without making actual changes.
    /// </summary>
    public bool DryRun { get; set; } = false;
}
